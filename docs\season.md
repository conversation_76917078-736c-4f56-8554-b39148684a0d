# 赛季与奖杯系统文档

## 概述

赛季与奖杯系统是游戏中的核心竞技系统，提供基于奖杯积分的段位划分、赛季周期管理和奖励发放功能。系统包含普通积分段位、赛季积分段位、段位奖励、赛季重置等完整的竞技体验，为玩家提供长期的竞技目标和成就感。

## 功能架构

### 核心模块
- **Trophy模块**：奖杯积分管理，处理战斗结算、段位计算、奖励发放
- **SeasonSystem模块**：全局赛季管理，处理赛季周期、重置逻辑、排行榜奖励
- **Battle模块**：战斗结算接口，与Trophy模块协作处理战斗结果

### 数据结构

#### 奖杯数据存储 (Trophy)
```protobuf
message Trophy {
    int32 current_trophy = 1;                    // 当前总奖杯数
    int32 current_win_streak = 2;                // 当前连胜次数
    repeated int32 claimed_rewards = 3;          // 已领取的普通段位奖励ID列表
    int32 current_season_id = 4;                 // 数据最后同步的赛季ID
    repeated int32 claimed_season_rewards = 5;   // 当前赛季已领取的段位奖励ID列表
    repeated SeasonTrophyInfo season_history = 6; // 历史赛季奖杯数据
    int32 supply_times = 7;                      // "对战补给"已用次数
    int32 blessing_times = 8;                    // "对战庇佑"已用次数
    int64 last_daily_reset_time = 9;             // 上次每日重置时间
}
```

#### 赛季历史信息 (SeasonTrophyInfo)
```protobuf
message SeasonTrophyInfo {
    int32 season_id = 1;      // 赛季ID
    int32 final_trophy = 2;   // 该赛季最终奖杯数
}
```

## 配置表结构

### 主要配置表

#### TableMainRank - 段位配置表
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 段位ID |
| ScoreRank | int32 | 达到该段位所需奖杯数 |
| ScoreReset | int32 | 赛季重置后的奖杯数 |
| ScoreType | int32 | 积分类型 (1-普通积分, 2-赛季积分) |
| MatchType | int32 | 匹配类型 |
| Area | int32[][] | 匹配分范围 |
| ScoreBattle | int32[] | 战斗得分 [第1名, 第2名, 第3名, 第4名] |
| Name | int32 | 段位名称ID |
| Icon | int32 | 段位图标ID |
| Reward | int32[][] | 段位奖励 [[道具ID, 数量], ...] |
| RewardBattle | int32[][] | 战斗奖励 [[道具ID, 数量], ...] |
| RewardDaily | int32[][] | 每日奖励 [[道具ID, 数量], ...] |
| RewardSeason | int32[][] | 赛季结算奖励 [[道具ID, 数量], ...] |

#### TableMainRankSeason - 赛季排行榜奖励配置
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 配置ID |
| Ranking | int32[] | 排行区间 [起始排名, 结束排名] |
| Reward | int32[][] | 奖励内容 [[道具ID, 数量], ...] |

### 全局配置

#### GameConfig相关配置
- `MainBattleDailyWinTimes`: 每日对战补给次数上限
- `MainBattleDailyFailTimes`: 每日对战庇佑次数上限  
- `MainRankScoreInitial`: 账号初始奖杯数
- `MainRankSeasonTime`: 赛季周期天数 (默认14天)

## 系统设计详解

### 1. 段位系统

#### 段位计算逻辑（已优化）
```go
// 段位范围缓存项
type RankRange struct {
    MinTrophy int32 // 最小奖杯数（包含）
    RankId    int32 // 段位ID
}

// 构建段位范围缓存（懒加载）
func (t *Trophy) buildRankCache() {
    // 收集所有段位配置并按ScoreRank升序排序
    ranks := make([]*table_data.TableMainRank, 0)
    table.GetTable().TableMainRank.Foreach(func(rank *table_data.TableMainRank) bool {
        ranks = append(ranks, rank)
        return false
    })

    sort.Slice(ranks, func(i, j int) bool {
        return ranks[i].ScoreRank < ranks[j].ScoreRank
    })

    // 构建缓存数组
    t.rankCache = make([]RankRange, len(ranks))
    for i, rank := range ranks {
        t.rankCache[i] = RankRange{
            MinTrophy: rank.ScoreRank,
            RankId:    rank.ID,
        }
    }
}

// 根据当前奖杯数获取段位ID（二分查找优化版本）
func (t *Trophy) GetCurrentRankId() int32 {
    // 懒加载：缓存为空时构建
    if len(t.rankCache) == 0 {
        t.buildRankCache()
    }

    currentTrophy := t.db.CurrentTrophy

    // 使用二分查找找到最后一个满足 MinTrophy <= currentTrophy 的位置
    // 时间复杂度 O(log n)
    left, right := 0, len(t.rankCache)-1
    result := 0

    for left <= right {
        mid := (left + right) / 2
        if t.rankCache[mid].MinTrophy <= currentTrophy {
            result = mid
            left = mid + 1
        } else {
            right = mid - 1
        }
    }

    return t.rankCache[result].RankId
}
```

#### 积分类型区分
- **普通积分 (ScoreType=1)**：基础段位系统，段位奖励一次性领取，不会重置
- **赛季积分 (ScoreType=2)**：达到普通积分最高段位后的进阶系统，每赛季重置奖励领取状态

### 2. 战斗结算系统

#### 奖杯变化计算
```go
func (t *Trophy) calculateTrophyChange(rank int32) int32 {
    // 获取当前段位
    rankId := t.GetCurrentRankId()
    rankConfig := table.GetTable().TableMainRank.GetById(rankId)
    
    // 根据排名获取奖杯变化（排名从1开始，数组从0开始）
    if rank >= 1 && rank <= int32(len(rankConfig.ScoreBattle)) {
        return rankConfig.ScoreBattle[rank-1]
    }
    
    return 0
}
```

#### 战斗结算流程
```
战斗结束 → 计算奖杯变化 → 更新奖杯数 → 发放战斗奖励 → 同步段位信息 → 返回结算通知
```

### 3. 赛季管理系统

#### 赛季周期计算
```go
func (s *SeasonSystem) OnStarted() {
    openServerTime := global.OpenServerTime
    seasonDays := s.getSeasonDays() // 14天
    currentTime := time.Now().Unix()
    seasonDuration := int64(seasonDays * 24 * 60 * 60)
    
    // 计算当前是第几个赛季（从1开始）
    elapsedTime := currentTime - openServerTime
    s.currentSeasonId = int32(elapsedTime/seasonDuration) + 1
    
    // 计算当前赛季的结束时间
    s.seasonEndTime = openServerTime + int64(s.currentSeasonId)*seasonDuration
}
```

#### 赛季重置流程
```
赛季结束检测 → 处理排行榜奖励 → 广播重置指令 → 玩家数据重置 → 进入新赛季
```

### 4. 奖励系统

#### 奖励类型
1. **战斗奖励 (RewardBattle)**：每次战斗结束后发放
2. **段位奖励 (Reward)**：达到段位后可领取，普通积分段位一次性
3. **每日奖励 (RewardDaily)**：对战补给功能，每日限次
4. **赛季奖励 (RewardSeason)**：赛季积分段位奖励，每赛季重置
5. **排行榜奖励**：赛季结束时根据排名发放

## 协议流程

### 赛季信息同步
```
客户端 → CLSeasonInfoReq → 服务器
服务器 → LCSeasonInfoRsp → 客户端
```

### 赛季重置通知
```
SeasonSystem → SeasonResetReq → PlayerSystem → 所有玩家
服务器 → LCSeasonResetNotify → 客户端
```

### 段位奖励领取
```
客户端 → CLClaimSeasonRewardReq → 服务器
服务器 → LCClaimSeasonRewardRsp → 客户端
```

## 核心接口

### Trophy模块接口

#### 基础信息获取
```go
// 获取当前奖杯数
func (t *Trophy) GetCurrentTrophy() int32

// 获取当前段位ID
func (t *Trophy) GetCurrentRankId() int32

// 获取剩余补给次数
func (t *Trophy) GetRemainingSupplyTimes() int32
```

#### 战斗相关
```go
// 处理战斗结束结算
func (t *Trophy) ProcessBattleEnd(rank int32, winStreak int32, finalLineup []*public.PBBattleHeroInfo) *cs.LCBattleEndNotify
```

#### 奖励相关
```go
// 领取段位奖励
func (t *Trophy) ClaimRankReward(rankId int32) error_code.Code

// 领取赛季段位奖励
func (t *Trophy) ClaimSeasonRankReward(rankId int32) error_code.Code
```

#### 赛季管理
```go
// 重置赛季（由SeasonSystem调用）
func (t *Trophy) ResetSeason(oldSeasonId, newSeasonId int32)
```

### SeasonSystem模块接口

#### 系统管理
```go
// 启动赛季系统
func (s *SeasonSystem) OnStarted()

// 处理赛季结束
func (s *SeasonSystem) handleSeasonEnd()
```

## 使用示例

### 战斗结算示例
```go
// 在Battle模块中处理战斗结束
func (b *Battle) ProcessBattleEnd(req *natsrpc.BattleEndReq) {
    // 调用Trophy模块处理战斗结算
    notify := b.player.Trophy().ProcessBattleEnd(req.Rank, req.WinStreak, req.Heros)
    
    // 发送结算通知给客户端
    b.player.NotifyBattleEnd(notify)
}
```

### 段位奖励领取示例
```go
// 处理段位奖励领取请求
func HandleClaimRankReward(player *Player, rankId int32) error_code.Code {
    return player.Trophy().ClaimRankReward(rankId)
}
```

### 赛季信息查询示例
```go
// 处理赛季信息请求
func (t *Trophy) HandleSyncSeasonInfo(req *cs.CLSeasonInfoReq) *cs.LCSeasonInfoRsp {
    rsp := &cs.LCSeasonInfoRsp{
        Code: int32(error_code.ERROR_OK),
        Trophy: t.db.CurrentTrophy,
        CurrentWinStreak: t.db.CurrentWinStreak,
        CurrentSeasonId: t.db.CurrentSeasonId,
    }
    
    // 填充段位奖励状态
    table.GetTable().TableMainRank.Foreach(func(rank *table_data.TableMainRank) bool {
        if rank.ScoreType == 1 { // 普通积分段位
            state := &public.PBRankRewardState{
                RankId: rank.ID,
                IsClaimed: t.IsRewardClaimed(rank.ID),
            }
            rsp.RankRewardStates = append(rsp.RankRewardStates, state)
        }
        return false
    })
    
    return rsp
}
```

## 错误码定义

### 奖杯相关错误码
| 错误码 | 常量名 | 说明 |
|--------|--------|------|
| ERROR_PARAMS | 参数错误 | 段位ID无效或其他参数错误 |
| ERROR_TROPHY_RANK_NOT_REACHED | 段位未达到 | 当前奖杯数未达到该段位要求 |
| ERROR_TROPHY_REWARD_ALREADY_CLAIMED | 奖励已领取 | 该段位奖励已经领取过 |
| ERROR_TROPHY_SEASON_REWARD_NOT_AVAILABLE | 赛季奖励不可用 | 非赛季积分段位或配置错误 |

## 数据流转

### 战斗结算数据流
```
BattleServer → GameServer (RPC) → Battle模块 → Trophy模块 → 数据库更新 → 客户端通知
```

### 赛季重置数据流
```
SeasonSystem定时检查 → 赛季结束处理 → PlayerSystem广播 → 所有玩家Trophy模块 → 数据重置 → 客户端通知
```

### 段位奖励数据流
```
客户端请求 → Trophy模块验证 → 奖励发放 → Item模块 → 数据库更新 → 客户端响应
```

## 配置示例

### MainRank配置示例
```json
{
    "ID": 1,
    "ScoreRank": 0,
    "ScoreReset": 0,
    "ScoreType": 1,
    "MatchType": 1,
    "Area": [[0, 999]],
    "ScoreBattle": [50, 20, 0, -10],
    "Name": 10001,
    "Icon": 20001,
    "Reward": [[1001, 100], [1002, 50]],
    "RewardBattle": [[1003, 10]],
    "RewardDaily": [[1004, 20]],
    "RewardSeason": []
}
```

### 配置说明
- **ScoreBattle**: [第1名+50, 第2名+20, 第3名+0, 第4名-10]
- **ScoreType**: 1表示普通积分段位，2表示赛季积分段位
- **ScoreReset**: 赛季重置后的奖杯数，仅对赛季积分段位有效

## 运行时机制

### 每日重置机制
```go
func (t *Trophy) checkDailyReset() {
    now := time.Now()
    today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
    todayUnix := today.Unix()

    if t.db.LastDailyResetTime < todayUnix {
        // 重置每日次数
        t.db.SupplyTimes = 0
        t.db.BlessingTimes = 0
        t.db.LastDailyResetTime = todayUnix
    }
}
```

### 赛季重置计算
```go
func (t *Trophy) calculateSeasonResetTrophy() int32 {
    currentTrophy := t.db.CurrentTrophy
    var resetTrophy int32 = currentTrophy

    // 查找对应的重置配置
    table.GetTable().TableMainRank.Foreach(func(rank *table_data.TableMainRank) bool {
        if currentTrophy >= rank.ScoreRank && rank.ScoreReset > 0 {
            resetTrophy = rank.ScoreReset
        }
        return false
    })

    return resetTrophy
}
```

## 性能优化

### 1. 段位计算优化（已实施）
- **懒加载缓存策略**：首次调用时构建段位范围缓存，后续查找O(log n)时间复杂度
- **二分查找算法**：使用有序数组+二分查找，适合大量数据的范围查找场景
- **数据规模**：MainRank表有94条记录，从O(94)线性查找优化到O(log 94)≈O(7)
- **性能提升**：从每次O(n)遍历优化到O(log n)查找，显著提升高频调用场景性能

#### 优化前后对比
```go
// 优化前：每次都要遍历整个配置表
func (t *Trophy) GetCurrentRankId() int32 {
    // O(n) 时间复杂度，每次调用都遍历
    table.GetTable().TableMainRank.Foreach(...)
}

// 优化后：懒加载缓存 + O(log n)二分查找
func (t *Trophy) GetCurrentRankId() int32 {
    if len(t.rankCache) == 0 {
        t.buildRankCache() // 仅首次调用
    }
    // O(log n) 时间复杂度，二分查找
    // 94条记录的查找次数：log₂(94) ≈ 7次
    left, right := 0, len(t.rankCache)-1
    // 二分查找逻辑...
}
```

#### 缓存失效机制
- **配置表热更新**：需要清空rankCache，下次调用时重建
- **数据一致性**：缓存构建失败时自动回退到原始遍历逻辑

### 2. 数据存储优化
- 使用增量更新，只同步变化的数据
- 历史赛季数据采用追加方式，避免大量数据重写

### 3. 内存管理
- 懒加载配置表数据
- 及时清理过期的临时数据

## 测试建议

### 功能测试
1. **基础功能**：奖杯计算、段位判定、奖励发放
2. **边界测试**：最高段位、最低奖杯数、赛季边界
3. **业务逻辑**：赛季重置、奖励领取限制、每日重置
4. **错误处理**：配置缺失、参数验证、异常情况

### 性能测试
1. **并发测试**：多玩家同时战斗结算
2. **赛季重置**：大量玩家同时重置的性能表现
3. **数据查询**：段位信息查询的响应时间
4. **内存使用**：长时间运行的内存占用情况

### 数据一致性测试
1. **跨模块交互**：与Battle、Item模块的数据交换
2. **事务完整性**：战斗结算失败时的数据回滚
3. **赛季数据**：赛季重置前后的数据一致性

## 注意事项

### 开发注意事项
1. **配置表依赖**：确保MainRank表正确配置ScoreBattle数组长度
2. **数据类型**：注意int32的使用范围，避免溢出
3. **时间处理**：赛季时间计算需考虑时区和夏令时
4. **错误处理**：完善的参数验证和错误码返回

### 运营注意事项
1. **赛季配置**：赛季周期调整需要重启服务器
2. **数据统计**：建议添加奖杯流水的BI统计
3. **排行榜奖励**：需要与排行榜系统和邮件系统集成
4. **配置热更**：段位配置的热更新机制

### 扩展性注意事项
1. **模块解耦**：保持Trophy和SeasonSystem的低耦合
2. **接口稳定**：对外接口的向后兼容性
3. **配置扩展**：预留配置字段用于功能扩展
4. **多赛季支持**：考虑未来多种赛季类型的扩展

---

*本文档基于当前代码实现编写，如有代码变更请及时更新文档内容。*
```
