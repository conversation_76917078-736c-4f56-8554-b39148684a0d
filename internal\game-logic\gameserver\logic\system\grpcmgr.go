package system

import (
	"context"
	chatv1 "liteframe/api/microservices/chat/v1"
	friendv1 "liteframe/api/microservices/friend/v1"
	playerv1 "liteframe/api/microservices/playerinfo/v1"
	rankv1 "liteframe/api/microservices/rank/v1"
	redeecodev1 "liteframe/api/microservices/redeemcode/v1"
	"liteframe/internal/common/constant"

	"github.com/go-kratos/kratos/contrib/registry/etcd/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
)

// GrpcMgr is a grpc manager.
type GrpcMgr struct {
	registry *etcd.Registry
	chat     chatv1.ChatServiceClient
	player   playerv1.PlayerInfoServiceClient
	redeem   redeecodev1.RedeemCodeClient
	rank     rankv1.RankServiceClient
	friend   friendv1.FriendserviceClient
}

func NewGrpcMgr(r *etcd.Registry) *GrpcMgr {
	return &GrpcMgr{
		registry: r,
		chat:     nil,
	}
}

func (g *GrpcMgr) Init() {
	g.chat = g.newChat()
	g.player = g.newPlayer()
	g.redeem = g.newRedeemCode()
	g.rank = g.newRank()
	g.friend = g.newFriend()
}

func (g *GrpcMgr) newChat() chatv1.ChatServiceClient {
	conn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithEndpoint("discovery:///"+constant.ServiceNameChat),
		grpc.WithDiscovery(g.registry),
	)
	if err != nil {
		log.Error("kind", "grpc-client", "reason", "GRPC_CLIENT_INIT_ERROR", "err", err)
		return nil
	}
	return chatv1.NewChatServiceClient(conn)
}

func (g *GrpcMgr) newPlayer() playerv1.PlayerInfoServiceClient {
	conn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithEndpoint("discovery:///"+constant.ServiceNamePlayerInfo),
		grpc.WithDiscovery(g.registry),
	)
	if err != nil {
		log.Error("kind", "grpc-client", "reason", "GRPC_CLIENT_INIT_ERROR", "err", err)
		return nil
	}
	return playerv1.NewPlayerInfoServiceClient(conn)
}

func (g *GrpcMgr) newRedeemCode() redeecodev1.RedeemCodeClient {
	conn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithEndpoint("discovery:///"+constant.ServiceNameRedeemCode),
		grpc.WithDiscovery(g.registry),
	)
	if err != nil {
		log.Error("kind", "grpc-client", "reason", "GRPC_CLIENT_INIT_ERROR", "err", err)
		return nil
	}
	return redeecodev1.NewRedeemCodeClient(conn)
}

func (g *GrpcMgr) newRank() rankv1.RankServiceClient {
	conn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithEndpoint("discovery:///"+constant.ServiceNameRank),
		grpc.WithDiscovery(g.registry),
	)
	if err != nil {
		log.Error("kind", "grpc-client", "reason", "GRPC_CLIENT_INIT_ERROR", "err", err)
		return nil
	}
	return rankv1.NewRankServiceClient(conn)
}

func (g *GrpcMgr) newFriend() friendv1.FriendserviceClient {
	conn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithEndpoint("discovery:///"+constant.ServiceNameFriend),
		grpc.WithDiscovery(g.registry),
	)
	if err != nil {
		log.Error("kind", "grpc-client", "reason", "GRPC_CLIENT_INIT_ERROR", "err", err)
		return nil
	}
	return friendv1.NewFriendserviceClient(conn)
}

func (g *GrpcMgr) GetChatService() chatv1.ChatServiceClient {
	return g.chat
}

func (g *GrpcMgr) GetPlayerService() playerv1.PlayerInfoServiceClient {
	return g.player
}

func (g *GrpcMgr) GetRedeemCodeService() redeecodev1.RedeemCodeClient {
	return g.redeem
}

func (g *GrpcMgr) GetRankService() rankv1.RankServiceClient {
	return g.rank
}

func (g *GrpcMgr) GetFriendService() friendv1.FriendserviceClient {
	return g.friend
}
